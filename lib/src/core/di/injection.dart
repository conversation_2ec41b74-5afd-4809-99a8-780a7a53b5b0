import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import 'injection.config.dart';

final GetIt getIt = GetIt.instance;

/// Configure dependencies for the application using Injectable
@InjectableInit(
  initializerName: 'init',
  preferRelativeImports: true,
  asExtension: true,
)
void configureDependencies() => getIt.init();

/// Reset the dependency injection container
/// Useful for testing
Future<void> resetDependencies() async {
  await getIt.reset();
}
