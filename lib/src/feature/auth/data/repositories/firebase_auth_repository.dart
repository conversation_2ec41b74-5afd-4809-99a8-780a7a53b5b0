import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:injectable/injectable.dart';

import '../../domain/entities/auth_user.dart';
import '../../domain/entities/otp_verification_request.dart';
import '../../domain/entities/phone_auth_request.dart';
import '../../domain/repositories/auth_repository.dart';
import '../models/auth_user_dto.dart';

/// Firebase implementation of AuthRepository
@LazySingleton(as: AuthRepository)
class FirebaseAuthRepository implements AuthRepository {
  const FirebaseAuthRepository(this._firebaseAuth);

  final FirebaseAuth _firebaseAuth;

  @override
  Stream<AuthUser?> get authStateChanges {
    return _firebaseAuth.authStateChanges().map((user) {
      if (user == null) return null;
      return AuthUserDto.fromFirebaseUser(user).toDomain();
    });
  }

  @override
  AuthUser? get currentUser {
    final user = _firebaseAuth.currentUser;
    if (user == null) return null;
    return AuthUserDto.fromFirebaseUser(user).toDomain();
  }

  @override
  Future<String> sendOtp(PhoneAuthRequest request) async {
    final completer = Completer<String>();

    await _firebaseAuth.verifyPhoneNumber(
      phoneNumber: request.completePhoneNumber,
      timeout: request.timeout ?? const Duration(seconds: 60),
      forceResendingToken: request.forceResendingToken,
      verificationCompleted: (PhoneAuthCredential credential) {
        // Auto-verification completed (Android only)
        // This will be handled in the presentation layer
      },
      verificationFailed: (FirebaseAuthException e) {
        if (!completer.isCompleted) {
          completer.completeError(e);
        }
      },
      codeSent: (String verificationId, int? resendToken) {
        if (!completer.isCompleted) {
          completer.complete(verificationId);
        }
      },
      codeAutoRetrievalTimeout: (String verificationId) {
        // Timeout occurred, but this is not an error
        if (!completer.isCompleted) {
          completer.complete(verificationId);
        }
      },
    );

    return completer.future;
  }

  @override
  Future<AuthUser> verifyOtp(OtpVerificationRequest request) async {
    final credential = PhoneAuthProvider.credential(
      verificationId: request.verificationId,
      smsCode: request.smsCode,
    );

    final userCredential = await _firebaseAuth.signInWithCredential(credential);

    if (userCredential.user == null) {
      throw Exception('Authentication failed: No user returned');
    }

    return AuthUserDto.fromFirebaseUser(userCredential.user!).toDomain();
  }

  @override
  Future<void> signOut() async {
    await _firebaseAuth.signOut();
  }

  @override
  Future<void> deleteAccount() async {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw Exception('No user is currently signed in');
    }

    await user.delete();
  }

  @override
  Future<void> refreshToken() async {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw Exception('No user is currently signed in');
    }

    await user.getIdToken(true); // Force refresh
  }

  @override
  Future<bool> isPhoneNumberRegistered(String phoneNumber) async {
    // Firebase doesn't provide a direct way to check if a phone number is registered
    // This would typically be implemented using a custom backend API
    // For now, we'll return false (allowing all phone numbers)
    return false;
  }
}
