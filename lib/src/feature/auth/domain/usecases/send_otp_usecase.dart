import 'package:injectable/injectable.dart';

import '../entities/phone_auth_request.dart';
import '../repositories/auth_repository.dart';

/// Use case for sending OTP to a phone number
@injectable
class SendOtpUseCase {
  const SendOtpUseCase(this._authRepository);

  final AuthRepository _authRepository;

  /// Execute the use case
  /// Returns verification ID on success
  Future<String> call(PhoneAuthRequest request) async {
    // Validate the request
    if (!request.isValid) {
      throw ArgumentError('Invalid phone number or country code');
    }

    // Check if phone number is already registered (optional business logic)
    // For now, we allow both new and existing users
    // This can be modified based on business requirements
    // await _authRepository.isPhoneNumberRegistered(request.completePhoneNumber);

    // Send OTP
    return await _authRepository.sendOtp(request);
  }
}
