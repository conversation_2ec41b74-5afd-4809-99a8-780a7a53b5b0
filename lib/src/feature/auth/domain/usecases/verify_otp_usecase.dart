import 'package:injectable/injectable.dart';

import '../entities/auth_user.dart';
import '../entities/otp_verification_request.dart';
import '../repositories/auth_repository.dart';

/// Use case for verifying OTP and completing authentication
@injectable
class VerifyOtpUseCase {
  const VerifyOtpUseCase(this._authRepository);

  final AuthRepository _authRepository;

  /// Execute the use case
  /// Returns authenticated user on success
  Future<AuthUser> call(OtpVerificationRequest request) async {
    // Validate the request
    if (!request.isValid) {
      throw ArgumentError('Invalid verification ID or SMS code');
    }

    // Verify OTP and get authenticated user
    final user = await _authRepository.verifyOtp(request);

    // Additional business logic can be added here
    // For example: logging, analytics, user profile setup, etc.

    return user;
  }
}
