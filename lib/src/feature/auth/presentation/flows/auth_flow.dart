import 'package:equatable/equatable.dart';
import 'package:flow_builder/flow_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/di/injection.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_state.dart';
import '../screens/phone_otp_screen.dart';
import '../screens/phone_registration_screen.dart';

/// Authentication flow states
enum AuthFlowState {
  phoneRegistration,
  otpVerification,
  authenticated,
}

/// Authentication flow data
class AuthFlowData extends Equatable {
  const AuthFlowData({
    required this.state,
    this.verificationId,
    this.user,
  });

  final AuthFlowState state;
  final String? verificationId;
  final dynamic user; // AuthUser from domain

  AuthFlowData copyWith({
    AuthFlowState? state,
    String? verificationId,
    dynamic user,
  }) {
    return AuthFlowData(
      state: state ?? this.state,
      verificationId: verificationId ?? this.verificationId,
      user: user ?? this.user,
    );
  }

  @override
  List<Object?> get props => [state, verificationId, user];
}

/// Authentication flow widget using flow_builder
class AuthFlow extends StatelessWidget {
  const AuthFlow({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<AuthBloc>(),
      child: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, authState) {
          return FlowBuilder<AuthFlowData>(
            state: _mapAuthStateToFlowData(authState),
            onGeneratePages: _onGeneratePages,
          );
        },
      ),
    );
  }

  /// Map AuthState to AuthFlowData
  AuthFlowData _mapAuthStateToFlowData(AuthState authState) {
    switch (authState.status) {
      case AuthStatus.initial:
      case AuthStatus.loading:
      case AuthStatus.error:
      case AuthStatus.unauthenticated:
        return const AuthFlowData(
          state: AuthFlowState.phoneRegistration,
        );

      case AuthStatus.otpSent:
        return AuthFlowData(
          state: AuthFlowState.otpVerification,
          verificationId: authState.verificationId,
        );

      case AuthStatus.authenticated:
        return AuthFlowData(
          state: AuthFlowState.authenticated,
          user: authState.user,
        );
    }
  }

  /// Generate pages based on flow state
  List<Page> _onGeneratePages(
    AuthFlowData flowData,
    List<Page<dynamic>> pages,
  ) {
    switch (flowData.state) {
      case AuthFlowState.phoneRegistration:
        return [
          MaterialPage(
            key: const ValueKey('phone_registration'),
            child: _buildPhoneRegistrationWithState(flowData),
          ),
        ];

      case AuthFlowState.otpVerification:
        return [
          MaterialPage(
            key: const ValueKey('phone_registration'),
            child: _buildPhoneRegistrationWithState(flowData),
          ),
          MaterialPage(
            key: const ValueKey('phone_otp'),
            child: _buildPhoneOtpWithState(flowData),
          ),
        ];

      case AuthFlowState.authenticated:
        // Authentication complete - this should trigger navigation to main app
        // The UserSessionListener will handle the actual navigation to home
        return [
          MaterialPage(
            key: const ValueKey('phone_registration'),
            child: _buildPhoneRegistrationWithState(flowData),
          ),
          MaterialPage(
            key: const ValueKey('phone_otp'),
            child: _buildPhoneOtpWithState(flowData),
          ),
        ];
    }
  }

  /// Build phone registration screen with flow state
  Widget _buildPhoneRegistrationWithState(AuthFlowData flowData) {
    return PhoneRegistrationScreen(
      key: ValueKey('phone_reg_${flowData.state.name}'),
      onNavigateToOtp: () {
        // Flow Builder handles navigation automatically through state changes
        // This callback is just for compatibility
      },
    );
  }

  /// Build phone OTP screen with flow state
  Widget _buildPhoneOtpWithState(AuthFlowData flowData) {
    return PhoneOtpScreen(
      verificationId: flowData.verificationId,
      key: ValueKey('phone_otp_${flowData.verificationId}'),
      onNavigateToHome: () {
        // Flow Builder handles navigation automatically through state changes
        // This callback is just for compatibility
      },
    );
  }
}
