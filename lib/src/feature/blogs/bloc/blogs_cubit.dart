import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shridattmandir/src/core/core.dart' show CubitExt;
import 'package:shridattmandir/src/feature/feature.dart'
    show BlogModel, BlogsRepository;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'blogs_state.dart';

@injectable
class BlogsCubit extends Cubit<BlogsState> {
  BlogsCubit() : super(const BlogsState());

  final BlogsRepository _blogsRepository = BlogsRepository(
    FirebaseFirestore.instance,
  );

  final TextEditingController authorController = TextEditingController();
  final TextEditingController dateController = TextEditingController();
  final TextEditingController titleController = TextEditingController();
  final TextEditingController contentController = TextEditingController();

  void clearControllers() {
    authorController.clear();
    dateController.clear();
    titleController.clear();
    contentController.clear();
  }

  Future<void> fetchBlogs() async {
    safeEmit(
      state.copyWith(
        loadMore: true,
      ),
    );
    try {
      final blogs = await _blogsRepository.fetchBlogs(
        documentSnapshot: state.lastVisible,
      );

      safeEmit(
        state.copyWith(
          blogs: [
            ...state.blogs ?? [],
            ...blogs.blogModel,
          ],
          lastVisible: blogs.lastVisibleDocumentSnapshot,
          hasReachedMax: blogs.blogModel.isEmpty,
        ),
      );
    } catch (error) {
      SdmToast.show(
        error.toString(),
      );
    }

    safeEmit(
      state.copyWith(
        loadMore: false,
      ),
    );
  }

  Future<void> addBlog() async {
    if (authorController.text.isEmpty ||
        dateController.text.isEmpty ||
        titleController.text.isEmpty ||
        contentController.text.isEmpty) {
      SdmToast.show('Please fill all the fields');
      return;
    }

    safeEmit(
      state.copyWith(
        loadMore: true,
      ),
    );

    try {
      final blog = BlogModel(
        author: authorController.text,
        date: dateController.text,
        title: titleController.text,
        description: contentController.text,
        timestamp: Timestamp.now(),
      );
      await _blogsRepository.addBlog(blog);
      SdmToast.show('Blog added successfully');
      clearControllers();
    } catch (error) {
      SdmToast.show(
        error.toString(),
      );
    }

    safeEmit(
      state.copyWith(
        loadMore: false,
      ),
    );
  }

  //zoom in and zoom out
  void zoomIn() {
    if (state.zoom >= 3) {
      return;
    }
    safeEmit(
      state.copyWith(
        zoom: state.zoom + 0.5,
      ),
    );
  }

  void zoomOut() {
    if (state.zoom <= 1) {
      return;
    }

    safeEmit(
      state.copyWith(
        zoom: state.zoom - 0.5,
      ),
    );
  }

  void disposeControllers() {
    authorController.dispose();
    dateController.dispose();
    titleController.dispose();
    contentController.dispose();
  }
}
