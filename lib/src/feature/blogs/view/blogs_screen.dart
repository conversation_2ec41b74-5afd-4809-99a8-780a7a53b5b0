import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show BlogBox, BlogsCubit, BlogsState;
import 'package:shridattmandir/src/shared/shared.dart' show SdmNoContent;
import 'package:very_good_infinite_list/very_good_infinite_list.dart';

class BlogsScreen extends StatelessWidget {
  const BlogsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Blogs',
          style:
              TextStyle(color: SdmPalette.black, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: Bloc<PERSON>uilder<BlogsCubit, BlogsState>(
        builder: (context, state) {
          return InfiniteList(
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 20.h,
              );
            },
            centerLoading: true,
            padding: EdgeInsets.only(
              left: 24.w,
              right: 24.w,
              bottom: 20.h,
              top: 20.h,
            ),
            debounceDuration: const Duration(
              milliseconds: 500,
            ),
            itemCount: state.blogs?.length ?? 0,
            onFetchData: context.read<BlogsCubit>().fetchBlogs,
            itemBuilder: (context, index) {
              final blog = state.blogs![index];
              return BlogBox(
                blogModel: blog,
              );
            },
            hasReachedMax: state.hasReachedMax,
            isLoading: state.loadMore,
            emptyBuilder: (context) => SdmNoContent(
              ctaText: 'Go Back',
              onCtaTap: () {
                context.router.pop();
              },
            ),
            loadingBuilder: (context) {
              return const Center(
                child: SizedBox(
                  height: 28,
                  width: 28,
                  child: CircularProgressIndicator.adaptive(),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
