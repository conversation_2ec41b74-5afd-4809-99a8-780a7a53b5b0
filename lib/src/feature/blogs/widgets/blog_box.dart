import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show BlogModel, AnalyticsCubit, BlogsReadClickEvent;

class BlogBox extends StatelessWidget {
  const BlogBox({
    super.key,
    required this.blogModel,
  });

  final BlogModel blogModel;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
              BlogsReadClickEvent(
                blogName: blogModel.title ?? '',
              ),
            );
        context.router.push(BlogViewRoute(blogModel: blogModel));
      },
      child: Container(
          height: 150.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.0).r,
            color: SdmPalette.white,
            boxShadow: [
              BoxShadow(
                color: SdmPalette.black29,
                offset: const Offset(0, 3),
                blurRadius: 6.r,
              )
            ],
          ),
          child: Padding(
              padding: const EdgeInsets.all(8.0).w,
              child: SingleChildScrollView(
                child: Column(
                  children: <Widget>[
                    SingleChildScrollView(
                      child: Column(
                        children: <Widget>[
                          Text(
                            blogModel.title ?? '',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16.sp,
                            ),
                          ),
                          Text(
                            '- ${blogModel.author}',
                            style: TextStyle(
                              color: SdmPalette.textColorGrey,
                              fontWeight: FontWeight.w600,
                              fontSize: 14.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 5.h,
                    ),
                    Text(
                      blogModel.description ?? '',
                      style: TextStyle(
                        fontSize: 14.sp,
                      ),
                    ),
                    SizedBox(
                      height: 5.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: <Widget>[
                        Text(
                          blogModel.date ?? '',
                          style: TextStyle(
                            color: SdmPalette.textColorGrey,
                            fontSize: 14.sp,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ))),
    );
  }
}
