import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show AccredibleCredentialsCertificates, CertificateBox;

class CertificatesScreen extends StatelessWidget {
  const CertificatesScreen({
    super.key,
    this.certificates,
  });

  final AccredibleCredentialsCertificates? certificates;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          "Certificates",
          style: TextStyle(
            color: SdmPalette.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        foregroundColor: SdmPalette.black,
        centerTitle: true,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 24).h,
        itemCount: certificates?.credentials?.length ?? 0,
        itemBuilder: (context, index) {
          final credential = certificates?.credentials?[index];
          return Column(
            children: [
              GestureDetector(
                onTap: () {
                  context.router
                      .push(CertificateDetailsRoute(credential: credential!));
                },
                child: CertificateBox(
                  thumbnailURL: credential?.seoImage,
                  title: credential?.name,
                  description: credential?.description,
                  url: credential?.courseLink,
                ),
              )
            ],
          );
        },
      ),
    );
  }
}
