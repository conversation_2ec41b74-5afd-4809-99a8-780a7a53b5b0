import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show Assets, SdmPalette;
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/certificate/repository/accredible_repository.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show CertificateCubit, CertificateState, CertificateStatus;
import 'package:shridattmandir/src/shared/shared.dart'
    show SdmPrimaryCta, SdmTextField, SdmToast;

class EnterEmailScreen extends StatefulWidget {
  const EnterEmailScreen({super.key});

  @override
  State<EnterEmailScreen> createState() => _EnterEmailScreenState();
}

class _EnterEmailScreenState extends State<EnterEmailScreen> {
  late TextEditingController emailController;

  @override
  void initState() {
    super.initState();
    emailController = TextEditingController();
  }

  @override
  void dispose() {
    emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => CertificateCubit(
        accredibleRepository: context.read<AccredibleRepository>(),
      ),
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'Get your certificates!',
            style: TextStyle(
              color: SdmPalette.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
          foregroundColor: SdmPalette.black,
        ),
        body: BlocConsumer<CertificateCubit, CertificateState>(
          listenWhen: (previous, current) => previous.status != current.status,
          listener: (context, state) {
            if (state.status == CertificateStatus.success) {
              if (state.certificates?.credentials?.isEmpty ?? true) {
                SdmToast.show(
                  'No certificates found for this email id.',
                );
                return;
              }
              context.router
                  .push(CertificatesRoute(certificates: state.certificates));
            }
          },
          builder: (context, state) {
            return Center(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Assets.sdmIcons.certificate.svg(
                      height: 100.h,
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 10,
                        right: 10,
                      ).w,
                      child: SdmTextField(
                        controller: emailController,
                        onChanged: (value) {
                          context
                              .read<CertificateCubit>()
                              .onEmailChanged(value);
                        },
                        keyboardType: TextInputType.emailAddress,
                        hintText: 'Enter your Email Id',
                        fontSize: 14.sp,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                            RegExp(r'[a-zA-Z0-9@.]'),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    if (state.status == CertificateStatus.loading)
                      const Center(
                        child: CircularProgressIndicator.adaptive(),
                      ),
                    if (!(state.status == CertificateStatus.loading))
                      Center(
                        child: SdmPrimaryCta(
                          onPressed: () async {
                            await context
                                .read<CertificateCubit>()
                                .getCertificates();
                          },
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0).w,
                            child: Text(
                              'Submit',
                              style: TextStyle(
                                color: SdmPalette.white,
                                fontSize: 14.sp,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
