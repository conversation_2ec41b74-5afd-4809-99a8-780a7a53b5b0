import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart'
    show SdmUrls, Assets, SdmPalette, SdmWebViewArguments;
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show ContactUsCubit, ContactUsState, SocialLinks;
import 'package:shridattmandir/src/shared/shared.dart'
    show SdmTextField, SdmPrimaryCta;

class ContactUsScreen extends StatelessWidget {
  static const String id = 'contact_us';

  const ContactUsScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ContactUsCubit(),
      child: BlocBuilder<ContactUsCubit, ContactUsState>(
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              iconTheme: const IconThemeData(color: SdmPalette.black),
              title: const Text(
                'Contact Us',
                style: TextStyle(
                    color: SdmPalette.black, fontWeight: FontWeight.bold),
              ),
              centerTitle: true,
            ),
            body: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(
                vertical: 16.0,
                horizontal: 16,
              ).w,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  SocialLinks(
                    text: 'www.shridatt.org',
                    url: SdmUrls.kWebsite,
                    icon: Assets.sdmIcons.webGlobe.svg(
                      height: 24.h,
                    ),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  SocialLinks(
                    text: '@shridattmandir',
                    url: SdmUrls.kInstagram,
                    icon: Assets.sdmIcons.instagram.svg(
                      height: 24.h,
                    ),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  SocialLinks(
                    text: '@shridattmandir',
                    url: SdmUrls.kFacebook,
                    icon: Assets.sdmIcons.facebook.svg(height: 24.h),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  SocialLinks(
                    text: '@shridattmandir',
                    url: SdmUrls.kTwitter,
                    icon: Assets.sdmIcons.twitter.svg(height: 24.h),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  SocialLinks(
                    text:
                        'D-8, Darshan Society, Gorai 2, Borivali West, Mumbai, Maharashtra 400091.',
                    url: SdmUrls.kGoogleMaps,
                    icon: Assets.sdmIcons.location.svg(
                      height: 24.h,
                    ),
                    textSize: 14.sp,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 30.h,
                      ),
                      const Text(
                        'FULL NAME',
                        style: TextStyle(
                          fontWeight: FontWeight.w700,
                          color: SdmPalette.textColorGrey2,
                        ),
                      ),
                      SizedBox(
                        height: 12.h,
                      ),
                      SdmTextField(
                        controller:
                            context.read<ContactUsCubit>().nameController,
                        keyboardType: TextInputType.text,
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      const Text(
                        'PHONE NUMBER',
                        style: TextStyle(
                          fontWeight: FontWeight.w700,
                          color: SdmPalette.textColorGrey2,
                        ),
                      ),
                      SizedBox(
                        height: 12.h,
                      ),
                      SdmTextField(
                        controller:
                            context.read<ContactUsCubit>().phoneController,
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(10),
                        ],
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      const Text(
                        'EMAIL',
                        style: TextStyle(
                          fontWeight: FontWeight.w700,
                          color: SdmPalette.textColorGrey2,
                        ),
                      ),
                      SizedBox(
                        height: 12.h,
                      ),
                      SdmTextField(
                        controller:
                            context.read<ContactUsCubit>().emailController,
                        keyboardType: TextInputType.emailAddress,
                        maxLines: 1,
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      const Text(
                        'MESSAGE',
                        style: TextStyle(
                          fontWeight: FontWeight.w700,
                          color: SdmPalette.textColorGrey2,
                        ),
                      ),
                      SizedBox(
                        height: 12.h,
                      ),
                      SdmTextField(
                        controller:
                            context.read<ContactUsCubit>().messageController,
                        maxLines: 5,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(500),
                        ],
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                    ],
                  ),
                  Center(
                    child: SdmPrimaryCta(
                      onPressed: () async {
                        await context.read<ContactUsCubit>().sendEmail();
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0).w,
                        child: Text(
                          'Submit',
                          style: TextStyle(
                            color: SdmPalette.white,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  Center(
                    child: Text.rich(
                      TextSpan(
                        children: [
                          if (Platform.isIOS)
                            TextSpan(
                              text: 'Terms of Use',
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w500,
                                color: SdmPalette.primary,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  context.router.push(
                                    SdmWebViewRoute(
                                      args: SdmWebViewArguments(
                                        uri: SdmUrls.kAppleTermsOfUse,
                                        title: 'Terms of Use',
                                      ),
                                    ),
                                  );
                                },
                            ),
                          if (Platform.isIOS)
                            TextSpan(
                              text: ' and ',
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w500,
                                color: SdmPalette.black54,
                              ),
                            ),
                          TextSpan(
                            text: 'Privacy Policy',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w500,
                              color: SdmPalette.primary,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                context.router.push(
                                  SdmWebViewRoute(
                                    args: SdmWebViewArguments(
                                      uri: SdmUrls.kPrivacyPolicy,
                                      title: 'Privacy Policy',
                                    ),
                                  ),
                                );
                              },
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 24.h,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
