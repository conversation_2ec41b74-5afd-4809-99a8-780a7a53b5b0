import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart';
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Builder(builder: (context) {
      final userSessionState = context.watch<UserSessionCubit>().state;

      return SizedBox(
        width: 0.6.sw,
        child: Theme(
          data: Theme.of(context).copyWith(
            canvasColor: SdmPalette.white.withValues(
              alpha: 0.89,
            ),
          ),
          child: Drawer(
            child: Padding(
              padding: const EdgeInsets.only(left: 8).w,
              child: Safe<PERSON>rea(
                child: ListView(
                  physics: const ClampingScrollPhysics(),
                  padding: EdgeInsets.zero,
                  children: <Widget>[
                    DrawerListTile(
                      title: 'Profile',
                      onTap: () {
                        context.router.pop();
                        context.router.push(const ProfileRoute());
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              ProfileClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'About Us',
                      onTap: () {
                        context.router.pop();
                        context.router.push(const AboutUsRoute());
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              AboutUsClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Music',
                      onTap: () async {
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              MusicClickEvent(),
                            );

                        // Use SubscriptionService for cleaner subscription checks
                        final subscriptionService = SubscriptionService(
                          purchasesCubit: context.read<PurchasesCubit>(),
                        );

                        if (subscriptionService.hasMusicAccess(
                          isAdmin: userSessionState.userProfile?.isAdmin,
                        )) {
                          context.router.pop();
                          context.router.push(const MusicListRoute());
                          return;
                        }

                        if (context.mounted) {
                          await PaywallManager.showPaywallModal(
                              context: context);
                        }
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Videos',
                      onTap: () {
                        context.router.pop();
                        context.router.push(const VideosRoute());
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              VideosClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Events',
                      onTap: () {
                        context.router.pop();
                        context.router.push(const CalendarEventsRoute());
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              CalendarClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Blogs',
                      onTap: () {
                        context.router.pop();
                        context.router.push(const BlogsRoute());

                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              BlogsClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Publications',
                      onTap: () {
                        context.router.pop();
                        context.router.push(const PublicationsRoute());

                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              PublicationsClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Get your certificates!',
                      onTap: () {
                        context.router.pop();
                        context.router.push(const EnterEmailRoute());
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              CertificatesClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Contact Us',
                      onTap: () {
                        context.router.pop();
                        context.router.push(const ContactUsRoute());

                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              ContactUsClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Info',
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return const DeviceInfoDialog();
                          },
                        );
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              InfoClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    if (userSessionState.userProfile?.isAdmin ?? false)
                      DrawerListTile(
                        title: 'Admin',
                        onTap: () {
                          context.router.pop();
                          context.router.push(const AdminHomeRoute());
                        },
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }
}
