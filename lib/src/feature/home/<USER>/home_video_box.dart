import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart';
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart';
import 'package:shridattmandir/src/shared/shared.dart';

class HomeVideoBox extends StatelessWidget {
  const HomeVideoBox({
    super.key,
    required this.videoModel,
  });

  final VideoModel videoModel;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
              VideosPlayEvent(
                id: videoModel.id ?? '',
                videoName: videoModel.title ?? '',
              ),
            );
        context.router.push(YoutubeVideoPlayerRoute(videoModel: videoModel));
      },
      child: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          Container(
            margin: EdgeInsets.only(
              bottom: 4.h,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.0),
              color: SdmPalette.white,
              boxShadow: const [
                BoxShadow(
                  color: SdmPalette.black29,
                  offset: Offset(0, 1),
                  blurRadius: 2,
                )
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(5.0).w,
              child: SdmNetworkImage(
                url: videoModel.thumbnailImage,
              ),
            ),
          ),
          Icon(
            Icons.play_arrow_rounded,
            size: 50.w,
            color: SdmPalette.white,
          )
        ],
      ),
    );
  }
}
