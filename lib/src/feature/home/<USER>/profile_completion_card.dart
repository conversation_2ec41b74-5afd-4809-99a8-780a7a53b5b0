import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart';
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/shared/shared.dart';

class ProfileCompletionCard extends StatelessWidget {
  const ProfileCompletionCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: SdmPalette.primary.withValues(alpha: .1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Text(
            'Complete Your Profile',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Please complete your profile to get personalized experience',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14.sp,
            ),
          ),
          Si<PERSON><PERSON><PERSON>(height: 12.h),
          SdmPrimaryCta(
            onPressed: () async {
              await context.router.push(const ProfileRoute());
              // Profile data will be automatically updated via UserSessionListener
            },
            child: Text(
              'Complete Now',
              style: TextStyle(
                color: SdmPalette.white,
                fontSize: 14.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
