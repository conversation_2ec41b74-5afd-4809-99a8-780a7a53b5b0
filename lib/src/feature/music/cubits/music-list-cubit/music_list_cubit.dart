import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shridattmandir/src/core/core.dart' show CubitExt;
import 'package:shridattmandir/src/feature/feature.dart'
    show IndexEnum, MusicModel, MusicRepository, UserModel;
import 'package:shridattmandir/src/feature/profile/repository/profile_repository.dart';
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'music_list_state.dart';

@injectable
class MusicListCubit extends Cubit<MusicListState> {
  MusicListCubit({
    required MusicRepository musicRepository,
    required ProfileRepository profileRepository,
  })  : _musicRepository = musicRepository,
        _profileRepository = profileRepository,
        super(const MusicListState());

  final MusicRepository _musicRepository;
  final ProfileRepository _profileRepository;

  void setIndexEnumAndResetList(IndexEnum? indexEnum) {
    safeEmit(
      state.copyWith(
        indexEnum: () => indexEnum,
        lastVisibleDocumentSnapshot: () => null,
        musicList: [],
      ),
    );
  }

  Future<void> fetchFavoritesFromUser() async {
    try {
      final user = await _profileRepository.getUserDataFromFirestore(
        uid: FirebaseAuth.instance.currentUser!.uid,
      );

      safeEmit(
        state.copyWith(
          favoriteMusicDocumentIds: user?.musicFavorites ?? [],
          user: user,
        ),
      );
    } catch (error, stackTrace) {
      SdmToast.show(
        error.toString(),
        stackTrace,
      );
    }
  }

  Future<void> fetchFavoritesMusic() async {
    final currentFetchedMusicDocumentIds = state.favoritesMusicList
        .map(
          (music) => music?.documentId,
        )
        .toList();

    final noneedToFetch = state.favoriteMusicDocumentIds?.every(
      (id) => currentFetchedMusicDocumentIds.contains(id),
    );

    if (noneedToFetch == true) {
      safeEmit(
        state.copyWith(
          favoritesMusicList: state.favoritesMusicList,
        ),
      );
      return;
    }

    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    await fetchFavoritesFromUser();

    await fetchMusicByFavoriteMusicIds();

    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  Future<void> setFavoritesToUser() async {
    if (state.user == null) {
      return;
    }

    if (state.user?.musicFavorites == state.favoriteMusicDocumentIds) {
      return;
    }
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );
    try {
      await _profileRepository.setUserDataToFirestore(
        user: state.user!.copyWith(
          musicFavorites: state.favoriteMusicDocumentIds,
        ),
      );

      await fetchFavoritesMusic();
    } catch (error, stackTrace) {
      SdmToast.show(
        error.toString(),
        stackTrace,
      );
    }
    safeEmit(
      state.copyWith(
        loading: false,
      ),
    );
  }

  void addRemoveFavorites({
    required MusicModel musicItem,
  }) {
    if (state.favoriteMusicDocumentIds?.contains(musicItem.documentId) ==
        true) {
      removeFavorites(
        documentId: musicItem.documentId ?? '',
      );
      SdmToast.show(
        'Removed from favorites',
      );
      return;
    }

    addFavorites(
      documentId: musicItem.documentId ?? '',
    );
  }

  void addFavorites({
    required String documentId,
  }) {
    if ((state.favoriteMusicDocumentIds?.length ?? 0) >= 51) {
      SdmToast.show(
        'Favorites limit reached',
      );
      return;
    }

    safeEmit(
      state.copyWith(
        favoriteMusicDocumentIds: [
          ...state.favoriteMusicDocumentIds ?? [],
          documentId,
        ],
      ),
    );

    SdmToast.show(
      'Added to favorites',
    );
  }

  void removeFavorites({
    required String documentId,
  }) {
    safeEmit(
      state.copyWith(
        favoriteMusicDocumentIds: state.favoriteMusicDocumentIds
            ?.where(
              (id) => id != documentId,
            )
            .toList(),
        favoritesMusicList: state.favoritesMusicList
            .where(
              (music) => music?.documentId != documentId,
            )
            .toList(),
      ),
    );
  }

  Future<void> fetchMusicByFavoriteMusicIds() async {
    safeEmit(
      state.copyWith(
        loading: true,
      ),
    );

    try {
      if (state.favoriteMusicDocumentIds == null ||
          state.favoriteMusicDocumentIds!.isEmpty) {
        safeEmit(
          state.copyWith(
            loading: false,
          ),
        );
        return;
      }

      final musicList = await Future.wait(
        state.favoriteMusicDocumentIds!.map(
          (id) => _musicRepository.fetchMusicById(id: id),
        ),
      );

      safeEmit(
        state.copyWith(
          loading: false,
        ),
      );
      if (musicList.isEmpty) {
        return;
      }

      safeEmit(
        state.copyWith(
          favoritesMusicList: musicList,
        ),
      );
    } catch (error, stackTrace) {
      SdmToast.show(
        error.toString(),
        stackTrace,
      );
    }
  }

  Future<void> fetchMusic() async {
    try {
      safeEmit(
        state.copyWith(
          loadMore: true,
        ),
      );
      final music = await _musicRepository.fetchMusic(
        documentSnapshot: state.lastVisibleDocumentSnapshot,
        indexEnum: state.indexEnum,
      );

      safeEmit(
        state.copyWith(
          loadMore: false,
        ),
      );

      if (state.indexEnum != null) {
        if (state.musicList != null) {
          List<MusicModel> updatedMusicList = [];
          updatedMusicList.addAll(music.musicModel);

          safeEmit(
            state.copyWith(
              musicList: [
                ...state.musicList ?? [],
                ...updatedMusicList,
              ],
              lastVisibleDocumentSnapshot: () =>
                  music.lastVisibleDocumentSnapshot,
              hasReachedMax: music.musicModel.isEmpty,
            ),
          );
        }
        return;
      }
      safeEmit(
        state.copyWith(
          musicList: [
            ...state.musicList ?? [],
            ...music.musicModel,
          ],
          lastVisibleDocumentSnapshot: () => music.lastVisibleDocumentSnapshot,
          hasReachedMax: music.musicModel.isEmpty,
        ),
      );
    } catch (error, stackTrace) {
      SdmToast.show(
        error.toString(),
        stackTrace,
      );
    }
  }
}
