import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart'
    show MusicPlayerArguments, SdmPalette;
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show
        AnalyticsCubit,
        MusicListCubit,
        MusicListState,
        MusicModel,
        MusicNowPlayingBottomBar,
        MusicPlayEvent,
        MusicPlayerCubit,
        MusicSongListTile,
        PlayerButtonState,
        RemoteConfigCubit;
import 'package:shridattmandir/src/shared/shared.dart';

class MusicFavoritesScreen extends StatelessWidget {
  const MusicFavoritesScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          await context.read<MusicListCubit>().setFavoritesToUser();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'Favourites',
            style: TextStyle(
              color: SdmPalette.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
        ),
        bottomNavigationBar: const MusicNowPlayingBottomBar(),
        body: BlocBuilder<MusicListCubit, MusicListState>(
          builder: (context, state) {
            return Stack(
              children: [
                IgnorePointer(
                  ignoring: state.loading,
                  child: ListView.builder(
                    itemCount: state.favoritesMusicList.length,
                    itemBuilder: (context, index) {
                      final musicItem = state.favoritesMusicList[index];
                      if (musicItem == null) {
                        return const SizedBox();
                      }
                      return Builder(builder: (context) {
                        final currentMusic =
                            context.select<MusicPlayerCubit, MusicModel?>(
                                (cubit) => cubit.state.currentMusic);
                        final playerButtonState =
                            context.select<MusicPlayerCubit, PlayerButtonState>(
                                (cubit) => cubit.state.playerButtonState);

                        final isPlaying =
                            currentMusic?.documentId == musicItem.documentId &&
                                playerButtonState == PlayerButtonState.playing;
                        return MusicSongListTile(
                          musicItem: musicItem,
                          isPlaying: isPlaying,
                          index: index,
                          onTap: () async {
                            final analyticsCubit =
                                context.read<AnalyticsCubit>();
                            final autoPlayLimit = context
                                .read<RemoteConfigCubit>()
                                .state
                                .autoPlayLimit;

                            final musicList =
                                state.favoritesMusicList.sublist(index);

                            context.router.push(
                              MusicPlayerRoute(
                                musicPlayerArguments: MusicPlayerArguments(
                                  musicList: musicList.cast<MusicModel>(),
                                  documentSnapshot:
                                      state.lastVisibleDocumentSnapshot,
                                  indexEnum: state.indexEnum,
                                  limit: autoPlayLimit,
                                ),
                              ),
                            );

                            analyticsCubit.onTrackAnalyticsEvent(
                              MusicPlayEvent(
                                musicName: musicList.first?.title ?? '',
                                id: musicList.first?.documentId ?? '',
                              ),
                            );
                          },
                        );
                      });
                    },
                  ),
                ),
                if (state.loading)
                  const Center(
                    child: CircularProgressIndicator.adaptive(),
                  ),
                if (state.favoritesMusicList.isEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0).w,
                    child: SdmNoContent(
                      ctaText: 'All Music',
                      onCtaTap: () {
                        Navigator.pop(context);
                      },
                      title: 'Swipe left on a song',
                      subtitle:
                          'No songs to your favourites yet\nTo add music to your favourites, swipe left on any song.',
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }
}
