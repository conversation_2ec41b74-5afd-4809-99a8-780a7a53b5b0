import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart'
    show MusicPlayerArguments, SdmPalette;
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart';
import 'package:shridattmandir/src/shared/shared.dart'
    show SdmNoContent, sdmBottomSheetCanvas;
import 'package:very_good_infinite_list/very_good_infinite_list.dart';

class MusicList extends StatefulWidget {
  const MusicList({
    super.key,
  });

  @override
  State<MusicList> createState() => _MusicListState();
}

class _MusicListState extends State<MusicList> {
  @override
  void initState() {
    super.initState();
    AppLifecycleListener(
      onStateChange: (lifeCycleState) async {
        //update the music list when app is closed, or gone in background
        if (lifeCycleState == AppLifecycleState.paused ||
            lifeCycleState == AppLifecycleState.detached) {
          if (mounted) {
            unawaited(context.read<MusicListCubit>().setFavoritesToUser());
          }
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MusicListCubit, MusicListState>(
      builder: (context, state) {
        return Scaffold(
          bottomNavigationBar: const MusicNowPlayingBottomBar(),
          appBar: AppBar(
            title: const Text(
              'Music',
              style: TextStyle(
                color: SdmPalette.black,
                fontWeight: FontWeight.bold,
              ),
            ),
            centerTitle: true,
            actions: [
              IconButton(
                onPressed: () {
                  sdmBottomSheetCanvas(
                    context,
                    BlocProvider.value(
                      value: context.read<MusicListCubit>(),
                      child: const MusicIndexBottomSheet(),
                    ),
                  );
                },
                tooltip: 'Filter',
                icon: const Icon(
                  Icons.filter_alt_outlined,
                ),
              ),
            ],
          ),
          body: PopScope(
            onPopInvokedWithResult: (didPop, result) {
              context.read<MusicListCubit>()
                ..setIndexEnumAndResetList(null)
                ..setFavoritesToUser()
                ..fetchMusic();
            },
            child: Stack(
              children: [
                IgnorePointer(
                  ignoring: state.loading,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (state.indexEnum != null)
                        Align(
                          alignment: Alignment.centerRight,
                          child: Padding(
                            padding: const EdgeInsets.only(right: 16).w,
                            child: const MusicClearFilterButton(),
                          ),
                        ),
                      Expanded(
                        child: InfiniteList(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.0.w,
                          ),
                          physics: const BouncingScrollPhysics(),
                          itemCount: state.musicList?.length ?? 0,
                          onFetchData:
                              context.read<MusicListCubit>().fetchMusic,
                          hasReachedMax: state.hasReachedMax,
                          isLoading: state.loadMore,
                          loadingBuilder: (context) => const Center(
                            child: SizedBox(
                              height: 28,
                              width: 28,
                              child: CircularProgressIndicator.adaptive(),
                            ),
                          ),
                          emptyBuilder: (context) => Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 30.0,
                            ),
                            child: SdmNoContent(
                              ctaText: state.indexEnum != null
                                  ? 'Clear Filter'
                                  : 'Go Back',
                              onCtaTap: () {
                                context.read<MusicListCubit>()
                                  ..setIndexEnumAndResetList(null)
                                  ..fetchMusic();
                                if (state.indexEnum == null) {
                                  Navigator.pop(context);
                                }
                              },
                            ),
                          ),
                          itemBuilder: (context, index) {
                            return MusicItem(
                              index: index,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                if (state.loading)
                  const Center(
                    child: CircularProgressIndicator.adaptive(),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class MusicItem extends StatelessWidget {
  const MusicItem({
    super.key,
    required this.index,
  });
  final int index;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MusicListCubit, MusicListState>(
      builder: (context, state) {
        final musicItem = state.musicList![index];

        final currentMusic = context.select<MusicPlayerCubit, MusicModel?>(
            (cubit) => cubit.state.currentMusic);
        final playerButtonState =
            context.select<MusicPlayerCubit, PlayerButtonState>(
                (cubit) => cubit.state.playerButtonState);

        final isPlaying = currentMusic?.documentId == musicItem.documentId &&
            playerButtonState == PlayerButtonState.playing;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (index == 0) const AlbumsSection(),
            MusicSongListTile(
              isPlaying: isPlaying,
              musicItem: musicItem,
              index: index,
              onTap: () async {
                final analyticsCubit = context.read<AnalyticsCubit>();
                final musicListCubit = context.read<MusicListCubit>();
                final autoPlayLimit =
                    context.read<RemoteConfigCubit>().state.autoPlayLimit;

                final musicList = state.musicList?.sublist(index);

                unawaited(musicListCubit.setFavoritesToUser());

                context.router.push(
                  MusicPlayerRoute(
                    musicPlayerArguments: MusicPlayerArguments(
                      musicList: musicList,
                      documentSnapshot: state.lastVisibleDocumentSnapshot,
                      indexEnum: state.indexEnum,
                      limit: autoPlayLimit,
                    ),
                  ),
                );

                analyticsCubit.onTrackAnalyticsEvent(
                  MusicPlayEvent(
                    musicName: musicList?.first.title ?? '',
                    id: musicList?.first.documentId ?? '',
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}
