import 'package:audio_video_progress_bar/audio_video_progress_bar.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmUrls, SdmPalette;
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show MusicPlayerCubit, MusicPlayerState, PlayerButtonState;
import 'package:shridattmandir/src/shared/shared.dart' show SdmNetworkImage;

class MusicNowPlayingBottomBar extends StatelessWidget {
  const MusicNowPlayingBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MusicPlayerCubit, MusicPlayerState>(
      builder: (context, state) {
        if (state.currentMusic == null) {
          return const SizedBox.shrink();
        }

        return SafeArea(
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              context.router.push(MusicPlayerRoute());
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ProgressBar(
                  onSeek: context.read<MusicPlayerCubit>().seek,
                  progressBarColor: SdmPalette.primary,
                  barHeight: 3.h,
                  bufferedBarColor: SdmPalette.white.withValues(alpha: 0.4),
                  thumbColor: SdmPalette.white,
                  baseBarColor: SdmPalette.white.withValues(alpha: 0.4),
                  thumbRadius: 0,
                  progress: state.positionDuration ?? Duration.zero,
                  total: state.totalDuration ?? Duration.zero,
                  timeLabelLocation: TimeLabelLocation.none,
                ),
                Container(
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: SdmPalette.primary,
                        width: 0.5.w,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      SdmNetworkImage(
                        url: ((state.currentMusic?.artUrl != null) &&
                                state.currentMusic!.artUrl!.isNotEmpty)
                            ? state.currentMusic?.artUrl
                            : SdmUrls.kMandirLogo,
                        height: 60.w,
                        width: 60.w,
                      ),
                      SizedBox(
                        width: 16.h,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              state.currentMusic?.title ?? '',
                              style: TextStyle(
                                fontSize: 15.sp,
                                fontWeight: FontWeight.w700,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Text(
                              state.currentMusic?.lyricist ?? '',
                              style: TextStyle(
                                color: SdmPalette.textColorGrey2,
                                fontSize: 13.sp,
                                fontWeight: FontWeight.w500,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      SizedBox(
                        width: 16.w,
                      ),
                      state.playerButtonState == PlayerButtonState.playing
                          ? IconButton(
                              iconSize: 28.w,
                              onPressed: () {
                                context.read<MusicPlayerCubit>().pause();
                              },
                              icon: Icon(
                                Icons.pause_rounded,
                                color: SdmPalette.lightRed,
                                size: 28.w,
                              ),
                            )
                          : state.playerButtonState == PlayerButtonState.loading
                              ? const CircularProgressIndicator.adaptive()
                              : IconButton(
                                  iconSize: 28.w,
                                  onPressed: () {
                                    context.read<MusicPlayerCubit>().play();
                                  },
                                  icon: Icon(
                                    Icons.play_arrow_rounded,
                                    color: SdmPalette.lightRed,
                                    size: 28.w,
                                  ),
                                ),
                      SizedBox(
                        width: 16.w,
                      ),

                      //close button
                      IconButton(
                        iconSize: 26.w,
                        onPressed: () async {
                          await context.read<MusicPlayerCubit>().stop();
                        },
                        icon: Icon(
                          Icons.close_rounded,
                          color: SdmPalette.lightRed,
                          size: 26.w,
                        ),
                      ),
                      SizedBox(
                        width: 16.w,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
