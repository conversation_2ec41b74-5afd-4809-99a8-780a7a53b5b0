import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart' show PublicationModel;
import 'package:shridattmandir/src/shared/shared.dart' show SdmNetworkImage;

class PublicationBox extends StatelessWidget {
  const PublicationBox({
    super.key,
    required this.publicationModel,
  });

  final PublicationModel publicationModel;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.router
            .push(PublicationInfoRoute(publicationModel: publicationModel));
      },
      child: Container(
        height: 150.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0).r,
          color: SdmPalette.white,
          boxShadow: [
            BoxShadow(
              color: SdmPalette.black29,
              offset: const Offset(0, 3),
              blurRadius: 6.r,
            )
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0).w,
          child: Row(
            children: <Widget>[
              SdmNetworkImage(
                url: publicationModel.image,
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Text(
                        publicationModel.title ?? '',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontWeight: FontWeight.w800,
                          fontSize: 16.sp,
                        ),
                      ),
                      SizedBox(
                        height: 6.h,
                      ),
                      Text(
                        publicationModel.description ?? '',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14.sp,
                        ),
                      ),
                      SizedBox(
                        height: 6.h,
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
