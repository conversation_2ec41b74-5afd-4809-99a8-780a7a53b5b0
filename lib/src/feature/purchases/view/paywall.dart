import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette, Assets;
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/core/router/models/sdm_web_view_arguments.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show AnalyticsCubit, HasMusicPremiumEvent, PurchasesCubit, PurchasesState;

class Paywall extends StatelessWidget {
  const Paywall({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PurchasesCubit, PurchasesState>(
      bloc: BlocProvider.of<PurchasesCubit>(context)..purchaseFlow(),
      listener: (context, state) async {
        if (state.hasMusicPremium) {
          context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                HasMusicPremiumEvent(
                    userId: state.customerInfo?.originalAppUserId ?? ''),
              );
          context.router.pop();
          context.router.push(const MusicListRoute());
        }
      },
      builder: (context, state) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 20.h,
            ),
            Text(
              'Subscribe to Premium',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 17.sp,
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            Center(
              child: Assets.sdmImages.subscribeIllustration.image(
                height: 88.h,
              ),
            ),
            SizedBox(
              height: 22.h,
            ),
            Flexible(
              child: ListView.builder(
                itemCount:
                    state.offerings?.current?.availablePackages.length ?? 0,
                itemBuilder: (BuildContext context, int index) {
                  final myProductList =
                      state.offerings?.current?.availablePackages ?? [];

                  return GestureDetector(
                    onTap: () async {
                      await context.read<PurchasesCubit>().purchaseOffering(
                            myProductList[index],
                          );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.r),
                          color: SdmPalette.primary,
                          boxShadow: [
                            BoxShadow(
                              color: SdmPalette.black29,
                              blurRadius: 3.r,
                              offset: Offset(
                                0,
                                3.h,
                              ),
                            )
                          ]),
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 8.h,
                      ),
                      margin: EdgeInsets.symmetric(
                        horizontal: 20.w,
                      ),
                      child: state.loading
                          ? Center(
                              child: const CircularProgressIndicator.adaptive(),
                            )
                          : Text(
                              '${myProductList[index].storeProduct.title} \n${myProductList[index].storeProduct.priceString} for ${context.read<PurchasesCubit>().getSubscriptionPeriod(myProductList[index].storeProduct.subscriptionPeriod ?? '')}',
                              style: TextStyle(
                                fontSize: 15.sp,
                                fontWeight: FontWeight.w700,
                                color: SdmPalette.white,
                              ),
                              textAlign: TextAlign.center,
                            ),
                    ),
                  );
                },
                shrinkWrap: true,
                physics: const ClampingScrollPhysics(),
              ),
            ),
            SizedBox(
              height: 30.h,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0).w,
              child: Text.rich(
                //terms of use and privacy policy
                TextSpan(
                  text: 'By continuing, you agree to our ',
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                    color: SdmPalette.black54,
                  ),
                  children: [
                    if (Platform.isIOS)
                      TextSpan(
                        text: 'Terms of Use',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                          color: SdmPalette.primary,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            context.router.push(
                              SdmWebViewRoute(
                                args: SdmWebViewArguments(
                                  uri:
                                      'https://www.apple.com/legal/internet-services/itunes/dev/stdeula/',
                                  title: 'Terms of Use',
                                ),
                              ),
                            );
                          },
                      ),
                    if (Platform.isIOS)
                      TextSpan(
                        text: ' and ',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                          color: SdmPalette.black54,
                        ),
                      ),
                    TextSpan(
                      text: 'Privacy Policy',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                        color: SdmPalette.primary,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          context.router.push(
                            SdmWebViewRoute(
                              args: SdmWebViewArguments(
                                uri: 'https://shridatt.org/privacy-policy',
                                title: 'Privacy Policy',
                              ),
                            ),
                          );
                        },
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 30.h,
            ),
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0).w,
                child: GestureDetector(
                  onTap: () async {
                    await context.read<PurchasesCubit>().restorePurchase();
                  },
                  child: Text(
                    'Restore Purchase',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 12.sp,
                      color: SdmPalette.primary,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 30.h,
            ),
          ],
        );
      },
    );
  }
}
