import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette;
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show AnalyticsCubit, VideosPlayEvent, VideoModel;
import 'package:shridattmandir/src/shared/shared.dart' show SdmNetworkImage;

class VideoBox extends StatelessWidget {
  const VideoBox({
    super.key,
    required this.videoModel,
  });

  final VideoModel videoModel;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
              VideosPlayEvent(
                id: videoModel.id ?? '',
                videoName: videoModel.title ?? '',
              ),
            );
        context.router.push(YoutubeVideoPlayerRoute(videoModel: videoModel));
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          color: SdmPalette.white,
        ),
        padding: const EdgeInsets.only(
          left: 10,
          right: 10,
          top: 10,
          bottom: 10,
        ).w,
        child: Column(
          children: <Widget>[
            Stack(
              alignment: Alignment.center,
              children: [
                SdmNetworkImage(
                  url: videoModel.thumbnailImage,
                ),
                Positioned.fill(
                  child: Icon(
                    Icons.play_arrow_rounded,
                    size: 50.w,
                    color: SdmPalette.white,
                  ),
                )
              ],
            ),
            SizedBox(
              height: 5.h,
            ),
            Text(
              videoModel.title ?? '',
              textAlign: TextAlign.center,
              softWrap: true,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: SdmPalette.textColorGrey,
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
