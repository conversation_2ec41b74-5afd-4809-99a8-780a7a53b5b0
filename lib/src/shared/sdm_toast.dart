import 'dart:developer';

import 'package:flutter/material.dart';

class SdmToast {
  static void show(String message,
      [StackTrace? stackTrace, BuildContext? context]) {
    // If context is provided, use it directly
    if (context != null) {
      _showSnackBar(context, message);
      log(message);
      if (stackTrace != null) {
        log(stackTrace.toString());
      }
      return;
    }

    // Fallback: just log the message if no context available
    log('SdmToast.show: $message');
    if (stackTrace != null) {
      log(stackTrace.toString());
    }
  }

  static void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
        ),
        animation: CurvedAnimation(
          parent: kAlwaysCompleteAnimation,
          curve: Curves.easeOutQuart,
          reverseCurve: Curves.easeInQuart,
        ),
        duration: Duration(
          seconds: 2,
        ),
        margin: EdgeInsets.all(16),
        padding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 14,
        ),
      ),
    );
  }
}
